rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Development mode - allow demo data seeding without authentication
    // TODO: Remove this in production and use proper authentication

    // Users can read and write their own user document, or allow all in dev mode
    match /users/{userId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Products - Allow all operations for demo data seeding
    match /products/{productId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Services - Allow all operations for demo data seeding
    match /services/{serviceId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Categories - Allow all operations for demo data seeding
    match /categories/{categoryId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Sales/Transactions - Allow all operations for demo data seeding
    match /sales/{saleId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    match /transactions/{transactionId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Inventory - Allow all operations for demo data seeding
    match /inventory/{inventoryId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }

    // Reports - Allow all operations for demo data seeding
    match /reports/{reportId} {
      allow read, write: if true; // Temporarily allow all for demo data seeding
    }
    
    // Helper function to get user role
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
  }
}
