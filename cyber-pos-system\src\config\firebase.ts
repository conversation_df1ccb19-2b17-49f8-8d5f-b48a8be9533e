import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import {
  getFirestore,
  enableNetwork,
  disableNetwork,
  connectFirestoreEmulator,
  persistentLocalCache
} from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
// Uses environment variables for different environments
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "demo-api-key",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "demo-cyber-pos",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "demo-app-id",
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID // Optional for Analytics
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services with modern persistence
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Enable offline persistence
try {
  enableNetwork(db);
} catch (error) {
  console.warn('Failed to enable network for Firestore:', error);
}

// Connect to emulators in development
const useEmulator = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';
if (process.env.NODE_ENV === 'development' && useEmulator) {
  try {
    // Connect to Auth emulator
    const authEmulatorHost = process.env.REACT_APP_FIREBASE_AUTH_EMULATOR_HOST || 'localhost:9099';
    connectAuthEmulator(auth, `http://${authEmulatorHost}`, { disableWarnings: true });

    // Connect to Firestore emulator
    const firestoreEmulatorHost = process.env.REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST || 'localhost:8080';
    const [firestoreHost, firestorePort] = firestoreEmulatorHost.split(':');
    connectFirestoreEmulator(db, firestoreHost, parseInt(firestorePort));

    // Connect to Storage emulator
    const storageEmulatorHost = process.env.REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
    const [storageHost, storagePort] = storageEmulatorHost.split(':');
    connectStorageEmulator(storage, storageHost, parseInt(storagePort));

    console.log('🔧 Connected to Firebase emulators');
  } catch (error) {
    console.log('⚠️ Emulators not available, using production Firebase:', error);
  }
}

// Offline persistence is now automatically enabled through FirestoreSettings.localCache
// No need for a separate enableOfflineSupport function with the new approach
export const enableOfflineSupport = async () => {
  // This function is kept for backward compatibility but is no longer needed
  // Persistence is automatically enabled through the localCache configuration above
  console.log('Firebase offline persistence is automatically enabled with the new configuration');
};

// Network status management
export const goOffline = async () => {
  try {
    await disableNetwork(db);
    console.log('Firebase network disabled');
  } catch (error) {
    console.error('Error disabling network:', error);
  }
};

export const goOnline = async () => {
  try {
    await enableNetwork(db);
    console.log('Firebase network enabled');
  } catch (error) {
    console.error('Error enabling network:', error);
  }
};

export default app;
